<div align="center">

<a href='https://nextchat.club'>
  <img src="https://github.com/user-attachments/assets/83bdcc07-ae5e-4954-a53a-ac151ba6ccf3" width="1000" alt="icon"/>
</a>

<h1 align="center">NextChat</h1>

영어 / [简体中文](./README_CN.md)

<a href="https://trendshift.io/repositories/5973" target="_blank">
  <img src="https://trendshift.io/api/badge/repositories/5973" alt="ChatGPTNextWeb%2FChatGPT-Next-Web | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/>
</a>

✨ 빠르고 가벼운 AI 어시스턴트, Claude, DeepSeek, GPT-4, Gemini Pro 지원

[![Saas][Saas-image]][saas-url]
[![Web][Web-image]][web-url]
[![Windows][Windows-image]][download-url]
[![MacOS][MacOS-image]][download-url]
[![Linux][Linux-image]][download-url]

[NextChatAI 웹사이트](https://nextchat.club?utm_source=readme) / [iOS 앱](https://apps.apple.com/us/app/nextchat-ai/id6743085599) / [웹 데모](https://app.nextchat.club) / [데스크톱 앱](https://github.com/Yidadaa/ChatGPT-Next-Web/releases) / [엔터프라이즈 버전](#enterprise-edition)

[saas-url]: https://nextchat.club?utm_source=readme
[saas-image]: https://img.shields.io/badge/NextChat-Saas-green?logo=microsoftedge
[web-url]: https://app.nextchat.club/
[download-url]: https://github.com/Yidadaa/ChatGPT-Next-Web/releases
[Web-image]: https://img.shields.io/badge/Web-PWA-orange?logo=microsoftedge
[Windows-image]: https://img.shields.io/badge/-Windows-blue?logo=windows
[MacOS-image]: https://img.shields.io/badge/-MacOS-black?logo=apple
[Linux-image]: https://img.shields.io/badge/-Linux-333?logo=ubuntu

[<img src="https://zeabur.com/button.svg" alt="Deploy on Zeabur" height="30">](https://zeabur.com/templates/ZBUEFA) [<img src="https://vercel.com/button" alt="Deploy on Vercel" height="30">](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2FChatGPTNextWeb%2FChatGPT-Next-Web&env=OPENAI_API_KEY&env=CODE&project-name=nextchat&repository-name=NextChat) [<img src="https://gitpod.io/button/open-in-gitpod.svg" alt="Open in Gitpod" height="30">](https://gitpod.io/#https://github.com/ChatGPTNextWeb/NextChat)

[<img src="https://github.com/user-attachments/assets/903482d4-3e87-4134-9af1-f2588fa90659" height="50" width="" >](https://monica.im/?utm=nxcrp)

</div>

## ❤️ AI API 후원사

<a href='https://302.ai/'>
  <img src="https://github.com/user-attachments/assets/a03edf82-2031-4f23-bdb8-bfc0bfd168a4" width="100%" alt="icon"/>
</a>

[302.AI](https://302.ai/)는 사용한 만큼만 비용을 지불하는 AI 애플리케이션 플랫폼으로, 다양한 AI API 및 온라인 애플리케이션을 제공합니다.

## 🥳 NextChat iOS 버전 출시!

> 👉 [지금 설치하기](https://apps.apple.com/us/app/nextchat-ai/id6743085599)

> ❤️ [소스 코드 곧 공개 예정](https://github.com/ChatGPTNextWeb/NextChat-iOS)

![Github iOS Image](https://github.com/user-attachments/assets/e0aa334f-4c13-4dc9-8310-e3b09fa4b9f3)

## 🫣 NextChat, MCP 지원!

> 빌드 전 환경 변수(env) `ENABLE_MCP=true` 설정 필요

<img src="https://github.com/user-attachments/assets/d8851f40-4e36-4335-b1a4-ec1e11488c7e" />

## 엔터프라이즈 버전

회사 내부 시스템에 맞춘 프라이빗 배포 및 맞춤형 커스터마이징 지원:

- **브랜드 커스터마이징**: 기업 이미지에 맞는 UI/UX 테마 적용
- **리소스 통합 관리**: 다양한 AI 모델을 통합하여 팀원이 손쉽게 사용 가능
- **권한 제어**: 관리자 패널을 통한 멤버·리소스·지식 베이스 권한 설정
- **지식 통합**: 사내 문서 및 데이터와 AI를 결합한 맞춤형 답변 제공
- **보안 감사**: 민감한 질문 차단 및 모든 기록 추적 가능
- **프라이빗 배포 지원**: 주요 클라우드 서비스에 맞춘 배포 옵션
- **지속적 업데이트**: 멀티모달 등 최신 AI 기능 지속 반영

엔터프라이즈 문의: **<EMAIL>**

## 🖼️ 스크린샷

![설정](./docs/images/settings.png)
![기타](./docs/images/more.png)

## 주요 기능 소개

- Vercel에서 원클릭 무료 배포 (1분 내 완성)
- 모든 OS(Linux/Windows/MacOS)에서 사용 가능한 클라이언트 (~5MB) [지금 다운 받기](https://github.com/Yidadaa/ChatGPT-Next-Web/releases)
- 자체 LLM 서버와 완벽 호환. [RWKV-Runner](https://github.com/josStorer/RWKV-Runner) 또는 [LocalAI](https://github.com/go-skynet/LocalAI)와 함께 사용하는 것을 추천
- 개인 정보 보호: 모든 대화 기록은 브라우저에만 저장
- Markdown 지원: LaTex, Mermaid, 코드 하이라이팅 등
- 반응형 디자인, 다크 모드, PWA 지원
- 빠른 초기 로딩 속도 (~100kb), 스트리밍 응답
- 프롬프트 템플릿 생성/공유/디버깅 지원 (v2)
- v2: 프롬프트 템플릿 기반 도구 생성, 공유, 디버깅 가능
- 고급 프롬프트 내장 [awesome-chatgpt-prompts-zh](https://github.com/PlexPt/awesome-chatgpt-prompts-zh) and [awesome-chatgpt-prompts](https://github.com/f/awesome-chatgpt-prompts)
- 긴 대화 내용 자동 압축 저장으로 토큰 절약
- I18n: English, 简体中文, 繁体中文, 日本語, Français, Español, Italiano, Türkçe, Deutsch, Tiếng Việt, Русский, Čeština, 한국어, Indonesia

<div align="center">
   
![主界面](./docs/images/cover.png)

</div>

## 개발 로드맵

- [x] 시스템 프롬프트: 사용자가 정의한 프롬프트를 시스템 프롬프트로 고정하기 [#138](https://github.com/Yidadaa/ChatGPT-Next-Web/issues/138)
- [x] 사용자 프롬프트: 사용자 정의 프롬프트를 편집 및 저장하여 리스트로 관리 가능
- [x] 프롬프트 템플릿: 사전 정의된 인컨텍스트 프롬프트로 새 채팅 생성 [#993](https://github.com/Yidadaa/ChatGPT-Next-Web/issues/993)
- [x] 이미지로 공유하거나 ShareGPT로 공유 [#1741](https://github.com/Yidadaa/ChatGPT-Next-Web/pull/1741)
- [x] Tauri 기반 데스크톱 앱
- [x] 자체 모델 호스팅: [RWKV-Runner](https://github.com/josStorer/RWKV-Runner), [LocalAI](https://github.com/go-skynet/LocalAI) 등 서버 배포 모델들과 완벽 호환 (llama, gpt4all, rwkv, vicuna, koala, gpt4all-j, cerebras, falcon, dolly 등)
- [x] 아티팩트: 생성된 콘텐츠 및 웹페이지를 별도 창으로 미리보기, 복사, 공유 가능 [#5092](https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web/pull/5092)
- [x] 플러그인: 웹 검색, 계산기, 기타 외부 API 기능 지원 [#165](https://github.com/Yidadaa/ChatGPT-Next-Web/issues/165) [#5353](https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web/issues/5353)
- [x] 실시간 채팅 지원 [#5672](https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web/issues/5672)
- [ ] 로컬 지식 베이스 지원 예정

## 🚀 최근 업데이트

- 🚀 v2.15.8 실시간 채팅 지원 [#5672](https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web/issues/5672)
- 🚀 v2.15.4 Tauri 기반 LLM API 호출 기능 추가 → 보안 강화 [#5379](https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web/issues/5379)
- 🚀 v2.15.0 플러그인 기능 추가 → [NextChat-Awesome-Plugins](https://github.com/ChatGPTNextWeb/NextChat-Awesome-Plugins)
- 🚀 v2.14.0 아티팩트 및 Stable Diffusion 기능 추가
- 🚀 v2.10.1 Google Gemini Pro 모델 지원
- 🚀 v2.9.11 Azure Endpoint 사용 가능
- 🚀 v2.8 모든 플랫폼에서 실행 가능한 클라이언트 출시
- 🚀 v2.7 대화 내용을 이미지로, 또는 ShareGPT로 공유 가능
- 🚀 v2.0 릴리즈: 프롬프트 템플릿 생성 및 아이디어 구현 가능! → [ChatGPT Prompt Engineering Tips](https://www.allabtai.com/prompt-engineering-tips-zero-one-and-few-shot-prompting/)

## 시작하기

1. [OpenAI API 키](https://platform.openai.com/account/api-keys)를 발급받습니다.
2. 
   [![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2FYidadaa%2FChatGPT-Next-Web&env=OPENAI_API_KEY&env=CODE&project-name=chatgpt-next-web&repository-name=ChatGPT-Next-Web) 버튼을 클릭해 Vercel에 배포합니다. `CODE`는 페이지 비밀번호라는 점을 기억하세요.

3. Enjoy :)

## FAQ

[FAQ](./docs/faq-ko.md)

## 최신 상태 유지 (Keep Updated)

Vercel로 배포한 경우, "Updates Available" 메시지가 계속 나타날 수 있습니다. 이는 프로젝트를 포크하지 않고 새로 생성했기 때문입니다.

다음 절차에 따라 다시 배포를 권장합니다:

1. 기존 레포 삭제
2. 우측 상단 "Fork" 버튼 클릭 → 포크 생성
3. 포크된 프로젝트를 다시 Vercel에 배포  
   → [자세한 튜토리얼 보기](./docs/vercel-ko.md)

### 자동 업데이트 활성화 (Enable Automatic Updates)

> Upstream Sync 오류 발생 시, [수동으로 코드 업데이트](./README_KO.md#manually-updating-code)하세요.

프로젝트 포크 후에는 GitHub의 제약으로 인해 Actions 페이지에서 아래 항목들을 수동으로 활성화해야 합니다:

- `Workflows`
- `Upstream Sync Action`

이후 매 시간 자동으로 업데이트됩니다:

![자동 업데이트 활성화](./docs/images/enable-actions.jpg)  
![업스트림 동기화 활성화](./docs/images/enable-actions-sync.jpg)

### 수동 업데이트 방법 (Manually Updating Code)

즉시 업데이트가 필요한 경우, [깃헙 문서](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/working-with-forks/syncing-a-fork)를 참고해 포크된 프로젝트를 upstream code와 동기화하세요.

릴리스 알림을 원하시면 star 또는 watch를 눌러주세요.

## 접근 비밀번호 설정 (Access Password)

이 프로젝트는 제한된 접근 제어를 제공합니다.  
Vercel 환경 변수에 `CODE`를 다음 형식으로 추가하세요. value는 ,를 통해 구분된 비밀번호여야 합니다.:

```
code1,code2,code3
```

수정 후 반드시 다시 배포해야 적용됩니다.

## 환경 변수 (Environment Variables)

### `CODE` (선택 사항)

접속 비밀번호. 쉼표로 구분합니다.

### `OPENAI_API_KEY` (필수)

당신의 OpenAI API 키, 여러 개를 사용하려면 쉼표로 연결합니다.

### `BASE_URL` (선택 사항)

> 기본값: `https://api.openai.com`

> 예시: `http://your-openai-proxy.com`

OpenAI API 요청의 기본 URL을 재정의합니다.

### `OPENAI_ORG_ID` (선택 사항)

OpenAI organization ID를 지정합니다.

### `AZURE_URL` (선택 사항)

> 예시: https://{azure-resource-url}/openai

Azure 배포 URL입니다.

### `AZURE_API_KEY` (선택 사항)

Azure API 키입니다.

### `AZURE_API_VERSION` (선택 사항)

Azure API 버전입니다. [Azure 문서](https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#chat-completions)에서 확인할 수 있습니다.

### `GOOGLE_API_KEY` (선택 사항)

Google Gemini Pro API 키입니다.

### `GOOGLE_URL` (선택 사항)

Google Gemini Pro API URL입니다.

### `ANTHROPIC_API_KEY` (선택 사항)

Anthropic Claude API 키입니다.

### `ANTHROPIC_API_VERSION` (선택 사항)

Anthropic Claude API 버전입니다.

### `ANTHROPIC_URL` (선택 사항)

Anthropic Claude API URL입니다.

### `BAIDU_API_KEY` (선택 사항)

Baidu API 키입니다.

### `BAIDU_SECRET_KEY` (선택 사항)

Baidu Secret 키입니다.

### `BAIDU_URL` (선택 사항)

Baidu API URL입니다.

### `BYTEDANCE_API_KEY` (선택 사항)

ByteDance API 키입니다.

### `BYTEDANCE_URL` (선택 사항)

ByteDance API URL입니다.

### `ALIBABA_API_KEY` (선택 사항)

Alibaba Cloud API 키입니다.

### `ALIBABA_URL` (선택 사항)

Alibaba Cloud API URL입니다.

### `IFLYTEK_URL` (선택 사항)

iflytek API URL입니다.

### `IFLYTEK_API_KEY` (선택 사항)

iflytek API 키입니다.

### `IFLYTEK_API_SECRET` (선택 사항)

iflytek API 시크릿입니다.

### `CHATGLM_API_KEY` (선택 사항)

ChatGLM API 키입니다.

### `CHATGLM_URL` (선택 사항)

ChatGLM API URL입니다.

### `DEEPSEEK_API_KEY` (선택 사항)

DeepSeek API 키입니다.

### `DEEPSEEK_URL` (선택 사항)

DeepSeek API URL입니다.

### `HIDE_USER_API_KEY` (선택 사항)

> 기본값: 비어 있음

사용자가 자신의 API 키를 입력하지 못하게 하려면 이 값을 1로 설정하세요.

### `DISABLE_GPT4` (선택 사항)

> 기본값: 비어 있음

사용자가 GPT-4를 사용하지 못하게 하려면 이 값을 1로 설정하세요.

### `ENABLE_BALANCE_QUERY` (선택 사항)

> 기본값: 비어 있음

사용자가 쿼리 잔액을 조회할 수 있도록 하려면 이 값을 1로 설정하세요.

### `DISABLE_FAST_LINK` (선택 사항)

> 기본값: 비어 있음

URL에서 설정을 파싱하는 기능을 비활성화하려면 이 값을 1로 설정하세요.

### `CUSTOM_MODELS` (선택 사항)

> 기본값: 비어 있음  
> 예시: `+llama,+claude-2,-gpt-3.5-turbo,gpt-4-1106-preview=gpt-4-turbo`  
이는 `llama`, `claude-2`를 모델 리스트에 추가하고, `gpt-3.5-turbo`를 제거하며, `gpt-4-1106-preview`를 `gpt-4-turbo`로 표시합니다.

사용자 지정 모델 제어 시 `+`는 추가, `-`는 제거, `이름=표시이름`은 모델명 커스터마이징을 의미합니다. 쉼표로 구분하세요.

- `-all`은 기본 모델을 모두 비활성화  
- `+all`은 기본 모델을 모두 활성화

Azure 용법 예시: `modelName@Azure=deploymentName` → 배포 이름을 커스터마이징 가능  
> 예시: `+gpt-3.5-turbo@Azure=gpt35` → 리스트에 `gpt35(Azure)` 표시됨  
> Azure 모델만 사용할 경우: `-all,+gpt-3.5-turbo@Azure=gpt35`

ByteDance 용법 예시: `modelName@bytedance=deploymentName`  
> 예시: `+Doubao-lite-4k@bytedance=ep-xxxxx-xxx` → `Doubao-lite-4k(ByteDance)`로 표시됨

### `DEFAULT_MODEL` (선택 사항)

기본 모델을 변경합니다.

### `VISION_MODELS` (선택 사항)

> 기본값: 비어 있음  
> 예시: `gpt-4-vision,claude-3-opus,my-custom-model`  
위의 모델들에 시각 기능을 부여합니다 (기본적으로 `"vision"`, `"claude-3"`, `"gemini-1.5"` 키워드를 포함한 모델은 자동 인식됨). 기본 모델 외에도 모델을 추가할 수 있습니다. 쉼표로 구분하세요.

### `WHITE_WEBDAV_ENDPOINTS` (선택 사항)

접속 허용할 WebDAV 서비스 주소를 늘리고자 할 때 사용합니다.

- 각 주소는 완전한 endpoint 여야 함: `https://xxxx/yyy`  
- 여러 주소는 `,`로 구분

### `DEFAULT_INPUT_TEMPLATE` (선택 사항)

설정 메뉴의 사용자 입력 전처리 구성 항목 초기화 시 사용할 기본 템플릿을 설정합니다.

### `STABILITY_API_KEY` (선택 사항)

Stability API 키입니다.

### `STABILITY_URL` (선택 사항)

Stability API URL을 커스터마이징합니다.

### `ENABLE_MCP` (선택 사항)

MCP (Model Context Protocol) 기능을 활성화합니다.

### `SILICONFLOW_API_KEY` (선택 사항)

SiliconFlow API 키입니다.

### `SILICONFLOW_URL` (선택 사항)

SiliconFlow API URL입니다.

### `AI302_API_KEY` (선택 사항)

302.AI API 키입니다.

### `AI302_URL` (선택 사항)

302.AI API URL입니다.

## 요구 사항 (Requirements)

NodeJS >= 18, Docker >= 20

## 개발 (Development)

[![Gitpod에서 열기](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/#https://github.com/Yidadaa/ChatGPT-Next-Web)

개발을 시작하기 전에 프로젝트 루트에 `.env.local` 파일을 만들고, 아래와 같이 API 키를 입력하세요:

```
OPENAI_API_KEY=<여기에 API 키 입력>

# OpenAI 서비스를 사용할 수 없는 경우 아래 BASE_URL 사용
BASE_URL=https://chatgpt1.nextweb.fun/api/proxy
```

### 로컬 개발 실행

```shell
# 1. Node.js와 Yarn을 먼저 설치
# 2. `.env.local` 파일에 환경 변수 설정
# 3. 실행
yarn install
yarn dev
```

## 배포 (Deployment)

### Docker (권장)

```shell
docker pull yidadaa/chatgpt-next-web

docker run -d -p 3000:3000 \
   -e OPENAI_API_KEY=sk-xxxx \
   -e CODE=your-password \
   yidadaa/chatgpt-next-web
```

서비스에 프록시를 사용하려면:

```shell
docker run -d -p 3000:3000 \
   -e OPENAI_API_KEY=sk-xxxx \
   -e CODE=your-password \
   -e PROXY_URL=http://localhost:7890 \
   yidadaa/chatgpt-next-web
```

프록시에 인증이 필요한 경우:

```shell
-e PROXY_URL="http://127.0.0.1:7890 user pass"
```

MCP를 활성화하려면:

```
docker run -d -p 3000:3000 \
   -e OPENAI_API_KEY=sk-xxxx \
   -e CODE=your-password \
   -e ENABLE_MCP=true \
   yidadaa/chatgpt-next-web
```

### 로컬 배포

콘솔에서 다음 명령을 실행하세요.

```shell
bash <(curl -s https://raw.githubusercontent.com/Yidadaa/ChatGPT-Next-Web/main/scripts/setup.sh)
```

⚠️ 참고: 설치 중에 문제가 발생하면 Docker 배포를 사용하세요.

## 채팅 기록 동기화 (UpStash)

| [简体中文](./docs/synchronise-chat-logs-cn.md) | [English](./docs/synchronise-chat-logs-en.md) | [Italiano](./docs/synchronise-chat-logs-es.md) | [日本語](./docs/synchronise-chat-logs-ja.md) | [한국어](./docs/synchronise-chat-logs-ko.md)

## 문서 (Documentation)

> 더 많은 문서는 [docs](./docs) 디렉토리를 참고하세요.

- [Cloudflare 배포 가이드 (폐기됨)](./docs/cloudflare-pages-ko.md)
- [자주 묻는 질문](./docs/faq-ko.md)
- [새 번역 추가 방법](./docs/translation.md)
- [Vercel 사용법 (중문)](./docs/vercel-cn.md)
- [사용자 매뉴얼 (중문, 작성 중)](./docs/user-manual-cn.md)

## 번역 (Translation)

새로운 번역을 추가하고 싶다면, [이 문서](./docs/translation.md)를 읽어보세요.

## 후원 (Donation)

[Buy Me a Coffee](https://www.buymeacoffee.com/yidadaa)

## 특별 감사 (Special Thanks)

### 기여자 (Contributors)

<a href="https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=ChatGPTNextWeb/ChatGPT-Next-Web" />
</a>

## 라이선스 (LICENSE)

[MIT](https://opensource.org/license/mit/)
