# Your openai api key. (required)
OPENAI_API_KEY=sk-xxxx

# DeepSeek Api Key. (Optional)
DEEPSEEK_API_KEY=

# Access password, separated by comma. (optional)
CODE=your-password

# You can start service behind a proxy. (optional)
PROXY_URL=http://localhost:7890

# Enable MCP functionality (optional)
# Default: Empty (disabled)
# Set to "true" to enable MCP functionality
ENABLE_MCP=

# (optional)
# Default: Empty
# Google Gemini Pro API key, set if you want to use Google Gemini Pro API.
GOOGLE_API_KEY=

# (optional)
# Default: https://generativelanguage.googleapis.com/
# Google Gemini Pro API url without pathname, set if you want to customize Google Gemini Pro API url.
GOOGLE_URL=

# Override openai api request base url. (optional)
# Default: https://api.openai.com
# Examples: http://your-openai-proxy.com
BASE_URL=

# Specify OpenAI organization ID.(optional)
# Default: Empty
OPENAI_ORG_ID=

# (optional)
# Default: Empty
# If you do not want users to use GPT-4, set this value to 1.
DISABLE_GPT4=

# (optional)
# Default: Empty
# If you do not want users to input their own API key, set this value to 1.
HIDE_USER_API_KEY=

# (optional)
# Default: Empty
# If you do want users to query balance, set this value to 1.
ENABLE_BALANCE_QUERY=

# (optional)
# Default: Empty
# If you want to disable parse settings from url, set this value to 1.
DISABLE_FAST_LINK=

# (optional)
# Default: Empty
# To control custom models, use + to add a custom model, use - to hide a model, use name=displayName to customize model name, separated by comma.
CUSTOM_MODELS=

# (optional)
# Default: Empty
# Change default model
DEFAULT_MODEL=

# anthropic claude Api Key.(optional)
ANTHROPIC_API_KEY=

### anthropic claude Api version. (optional)
ANTHROPIC_API_VERSION=

### anthropic claude Api url (optional)
ANTHROPIC_URL=

### (optional)
WHITE_WEBDAV_ENDPOINTS=

### siliconflow Api key (optional)
SILICONFLOW_API_KEY=

### siliconflow Api url (optional)
SILICONFLOW_URL=

### 302.AI Api key (optional)
AI302_API_KEY=

### 302.AI Api url (optional)
AI302_URL=
