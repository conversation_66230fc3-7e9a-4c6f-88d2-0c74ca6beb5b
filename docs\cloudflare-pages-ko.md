## Cloudflare 페이지 배포 가이드

## 새 프로젝트를 만드는 방법
이 프로젝트를 Github에서 포크한 다음 dash.cloudflare.com에 로그인하고 페이지로 이동합니다.

1. "프로젝트 만들기"를 클릭합니다.
2. "Git에 연결"을 선택합니다.
3. Cloudflare 페이지를 GitHub 계정과 연결합니다.
4. 포크한 프로젝트를 선택합니다.
5. "설정 시작"을 클릭합니다.
6. "프로젝트 이름" 및 "프로덕션 브랜치"의 기본값을 사용하거나 필요에 따라 변경합니다.
7. "빌드 설정"에서 "프레임워크 프리셋" 옵션을 선택하고 "Next.js"를 선택합니다.
8. node:buffer 버그로 인해 지금은 기본 "빌드 명령어"를 사용하지 마세요. 다음 명령을 사용하세요:
   ```
   npx @cloudflare/next-on-pages --experimental-minify
   ```
9. "빌드 출력 디렉토리"의 경우 기본값을 사용하고 수정하지 마십시오.
10. "루트 디렉토리"는 수정하지 마십시오.
11. "환경 변수"의 경우 ">"를 클릭한 다음 "변수 추가"를 클릭합니다. 다음에 따라 정보를 입력합니다:

    - node_version=20.1`.
    - next_telemetry_disable=1`.
    - `OPENAI_API_KEY=자신의 API 키`
    - ``yarn_version=1.22.19``
    - ``php_version=7.4``.

    실제 필요에 따라 다음 옵션을 선택적으로 입력합니다:

    - `CODE= 선택적으로 액세스 비밀번호를 입력하며 쉼표를 사용하여 여러 비밀번호를 구분할 수 있습니다`.
    - `OPENAI_ORG_ID= 선택 사항, OpenAI에서 조직 ID 지정`
    - `HIDE_USER_API_KEY=1 선택 사항, 사용자가 API 키를 입력하지 못하도록 합니다.
    - `DISABLE_GPT4=1 옵션, 사용자가 GPT-4를 사용하지 못하도록 설정` 12.
    
12. "저장 후 배포"를 클릭합니다.
13. 호환성 플래그를 입력해야 하므로 "배포 취소"를 클릭합니다.
14. "빌드 설정", "기능"으로 이동하여 "호환성 플래그"를 찾습니다.
15. "프로덕션 호환성 플래그 구성" 및 "프리뷰 호환성 플래그 구성"에서 "nodejs_compat"를 입력합니다.
16. "배포"로 이동하여 "배포 다시 시도"를 클릭합니다.
17. 즐기세요!